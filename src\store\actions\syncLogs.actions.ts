// SyncLogs-related async thunks and actions

import { createAsyncThunk } from '@reduxjs/toolkit';
import {
  SyncLog,
  SyncLogFilters,
  FetchSyncLogsResponse,
  RetrySyncOperationParams,
  RetrySyncOperationResponse
} from '../types/syncLogs.types';
import { syncLogsApi, CompanySyncLogFilters, CompanySyncStats, DetailedSyncLog } from '../../api/syncLogs.api';
import type { RootState, AppDispatch } from '../index';

// Async thunk to fetch sync logs with pagination
export const fetchSyncLogs = createAsyncThunk<
  {
    items: SyncLog[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  },
  CompanySyncLogFilters,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'syncLogs/fetchSyncLogs',
  async (filters, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.getCompanySyncLogs(filters);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch sync logs');
    }
  }
);

// Async thunk to retry sync operation
export const retrySyncOperation = createAsyncThunk<
  { success: boolean; message: string; newSyncLogId?: string },
  string, // syncLogId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'syncLogs/retrySyncOperation',
  async (syncLogId, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.retrySyncOperation(syncLogId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to retry sync operation');
    }
  }
);

// Async thunk to clear sync logs
export const clearSyncLogs = createAsyncThunk(
  'syncLogs/clearSyncLogs',
  async (_, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.clearSyncLogs();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to clear sync logs');
    }
  }
);

/**
 * Get sync logs with filtering & pagination for a specific company
 * GET /api/v1/companies/:companyId/sync-logs
 */
export const getCompanySyncLogs = createAsyncThunk<
  {
    items: SyncLog[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  },
  CompanySyncLogFilters,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'syncLogs/getCompanySyncLogs',
  async (filters, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.getCompanySyncLogs(filters);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get company sync logs');
    }
  }
);

/**
 * Get detailed sync log by ID
 * GET /api/v1/sync-logs/:syncLogId
 */
export const getDetailedSyncLog = createAsyncThunk<
  DetailedSyncLog,
  string, // syncLogId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'syncLogs/getDetailedSyncLog',
  async (syncLogId, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.getSyncLogDetails(syncLogId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get detailed sync log');
    }
  }
);

/**
 * Get sync statistics for company
 * GET /api/v1/companies/:companyId/sync-stats
 */
export const getCompanySyncStats = createAsyncThunk<
  CompanySyncStats,
  string, // companyId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'syncLogs/getCompanySyncStats',
  async (companyId, { rejectWithValue }) => {
    try {
      const response = await syncLogsApi.getCompanySyncStats(companyId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get company sync stats');
    }
  }
);
