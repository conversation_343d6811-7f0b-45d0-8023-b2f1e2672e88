// Auth Types
export interface CompanyInfo {
  name: string;
  shortCode: string;
  baseCurrency: string;
  countryCode: string;
  taxNumber: string;
  organisationType: string;
}

export interface AuthState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  companyInfo: CompanyInfo | null;
  accessToken: string | null;
  refreshToken: string | null;
}

// Companies Types
export interface Organization {
  id: string;
  name: string;
  shortCode: string;
  tenantId?: string;
  connectionId?: string;
  lastSync?: string;
}

export interface CompaniesState {
  organizations: Organization[];
  selectedOrganization: string | null;
  isLoading: boolean;
  error: string | null;
}

// Sync Logs Types
export interface SyncLog {
  id: string;
  timestamp: string;
  endpoint: string;
  status: 'success' | 'error' | 'warning';
  duration: string;
  message: string;
  responseBody?: string;
  requestId?: string;
  fullUrl?: string;
}

export interface SyncLogsState {
  logs: SyncLog[];
  filteredLogs: SyncLog[];
  filters: {
    startDate: string;
    endDate: string;
    endpoint: string;
    status: string;
    searchQuery: string;
  };
  selectedLog: SyncLog | null;
  isLoading: boolean;
  error: string | null;
}

// Settings Types
export interface SyncEntity {
  name: string;
  lastSync: string;
  status: 'success' | 'pending' | 'syncing';
  recordCount?: number;
  enabled: boolean;
}

export interface SettingsState {
  entities: SyncEntity[];
  syncSchedule: {
    enabled: boolean;
    frequency: 'hourly' | 'daily' | 'weekly';
    time: string;
  };
  isLoading: boolean;
  error: string | null;
}

// API Response Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
