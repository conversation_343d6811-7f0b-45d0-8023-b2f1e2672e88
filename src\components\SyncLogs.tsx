import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchSyncLogs,
  retrySyncOperation,
} from "@/store/actions/syncLogs.actions";
import {
  setFilters,
  setSelectedLog,
  clearError,
  setPagination,
} from "@/store/slices/syncLogsSlice";
import {
  Calendar,
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  FileText,
  Copy,
  Loader2,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  XCircle,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import useOrganizations from "@/hooks/useOrganizations";
import { debounce } from "@/utils/performance";

const SyncLogs: React.FC = () => {
  const dispatch = useAppDispatch();
  const { filteredLogs, filters, selectedLog, isLoading, pagination } =
    useAppSelector((state) => state.syncLogs);
  const { selectedOrganization } = useOrganizations();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [retryingLogs, setRetryingLogs] = useState<Set<string>>(new Set());

  // Function to fetch logs with current filters and pagination
  const fetchLogsWithFilters = useCallback(
    (customFilters?: any) => {
      if (!selectedOrganization) return;

      const finalFilters = {
        companyId: selectedOrganization,
        page: pagination?.page || 1,
        limit: pagination?.limit || 20,
        ...filters,
        ...customFilters,
      };

      dispatch(fetchSyncLogs(finalFilters));
    },
    [dispatch, selectedOrganization, filters, pagination]
  );

  // Fetch logs on component mount and when organization changes
  useEffect(() => {
    if (selectedOrganization) {
      fetchLogsWithFilters();
    }
  }, [selectedOrganization, pagination?.page, pagination?.limit]);

  // Filter handlers
  const handleFilterChange = (filterType: string, value: string) => {
    dispatch(setFilters({ [filterType]: value }));
    // Reset to first page when filters change
    dispatch(setPagination({ page: 1 }));
    if (selectedOrganization) {
      fetchLogsWithFilters({ [filterType]: value, page: 1 });
    }
  };

  const handleRefresh = () => {
    if (selectedOrganization) {
      fetchLogsWithFilters();
    }
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    dispatch(setPagination({ page }));
  };

  const handleLimitChange = (limit: number) => {
    dispatch(setPagination({ page: 1, limit }));
  };

  const handleLogClick = (log: any) => {
    dispatch(setSelectedLog(log));
    setIsDrawerOpen(true);
  };

  // Retry sync operation
  const handleRetrySync = async (syncLogId: string) => {
    try {
      setRetryingLogs((prev) => new Set(prev).add(syncLogId));

      toast.loading("Retrying sync operation...", {
        id: `retry-${syncLogId}`,
      });

      await dispatch(retrySyncOperation(syncLogId)).unwrap();

      toast.success("Sync operation retried successfully!", {
        id: `retry-${syncLogId}`,
        description: "The sync operation has been queued for retry.",
      });

      // Refresh the logs to show updated status
      handleRefresh();
    } catch (error) {
      console.error("Retry failed:", error);
      toast.error("Failed to retry sync operation", {
        id: `retry-${syncLogId}`,
        description:
          typeof error === "string" ? error : "Please try again later.",
      });
    } finally {
      setRetryingLogs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(syncLogId);
        return newSet;
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Success
          </Badge>
        );
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      case "warning":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            Warning
          </Badge>
        );
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const handleRowClick = (log: any) => {
    handleLogClick(log);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Sync Logs</h2>
          <p className="text-muted-foreground">
            Monitor synchronization history and troubleshoot issues
          </p>
        </div>
      </div>

      <Card className="shadow-sm border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <CardTitle className="text-lg font-semibold text-gray-800">
                Filters
              </CardTitle>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                dispatch(
                  setFilters({
                    startDate: "",
                    endDate: "",
                    endpoint: "all",
                    status: "all",
                    searchQuery: "",
                  })
                );
                dispatch(setPagination({ page: 1 }));
                if (selectedOrganization) {
                  fetchLogsWithFilters({
                    startDate: "",
                    endDate: "",
                    endpoint: "all",
                    status: "all",
                    searchQuery: "",
                    page: 1,
                  });
                }
              }}
              className="text-gray-600 hover:text-gray-800"
            >
              Clear All
            </Button>
          </div>
          <CardDescription className="text-gray-600">
            Filter sync logs by date range, entity, status, or search terms
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Quick Date Filters */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">
                Quick Date Filters:
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date().toISOString().split("T")[0];
                  dispatch(setFilters({ startDate: today, endDate: today }));
                  dispatch(setPagination({ page: 1 }));
                  if (selectedOrganization) {
                    fetchLogsWithFilters({
                      startDate: today,
                      endDate: today,
                      page: 1,
                    });
                  }
                }}
                className="text-xs"
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date();
                  const yesterday = new Date(today);
                  yesterday.setDate(yesterday.getDate() - 1);
                  const yesterdayStr = yesterday.toISOString().split("T")[0];
                  dispatch(
                    setFilters({
                      startDate: yesterdayStr,
                      endDate: yesterdayStr,
                    })
                  );
                  dispatch(setPagination({ page: 1 }));
                  if (selectedOrganization) {
                    fetchLogsWithFilters({
                      startDate: yesterdayStr,
                      endDate: yesterdayStr,
                      page: 1,
                    });
                  }
                }}
                className="text-xs"
              >
                Yesterday
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date();
                  const lastWeek = new Date(today);
                  lastWeek.setDate(lastWeek.getDate() - 7);
                  const startDate = lastWeek.toISOString().split("T")[0];
                  const endDate = today.toISOString().split("T")[0];
                  dispatch(setFilters({ startDate, endDate }));
                  dispatch(setPagination({ page: 1 }));
                  if (selectedOrganization) {
                    fetchLogsWithFilters({ startDate, endDate, page: 1 });
                  }
                }}
                className="text-xs"
              >
                Last 7 Days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date();
                  const lastMonth = new Date(today);
                  lastMonth.setDate(lastMonth.getDate() - 30);
                  const startDate = lastMonth.toISOString().split("T")[0];
                  const endDate = today.toISOString().split("T")[0];
                  dispatch(setFilters({ startDate, endDate }));
                  dispatch(setPagination({ page: 1 }));
                  if (selectedOrganization) {
                    fetchLogsWithFilters({ startDate, endDate, page: 1 });
                  }
                }}
                className="text-xs"
              >
                Last 30 Days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  dispatch(setFilters({ startDate: "", endDate: "" }));
                  dispatch(setPagination({ page: 1 }));
                  if (selectedOrganization) {
                    fetchLogsWithFilters({
                      startDate: "",
                      endDate: "",
                      page: 1,
                    });
                  }
                }}
                className="text-xs text-gray-500"
              >
                Clear Dates
              </Button>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {/* Search Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <Search className="h-3 w-3" />
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search logs, entities, messages..."
                  value={filters.searchQuery || ""}
                  onChange={(e) => {
                    const value = e.target.value;
                    dispatch(setFilters({ searchQuery: value }));
                    // Use debounced search for API calls
                    if (selectedOrganization) {
                      debouncedSearch(value);
                    }
                  }}
                  className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
                {filters.searchQuery && (
                  <button
                    onClick={() => {
                      dispatch(setFilters({ searchQuery: "" }));
                      if (selectedOrganization) {
                        debouncedSearch("");
                      }
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                Start Date
              </label>
              <div className="relative">
                <Input
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) =>
                    handleFilterChange("startDate", e.target.value)
                  }
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  max={filters.endDate || undefined}
                />
                {filters.startDate && (
                  <button
                    onClick={() => handleFilterChange("startDate", "")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                End Date
              </label>
              <div className="relative">
                <Input
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) =>
                    handleFilterChange("endDate", e.target.value)
                  }
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  min={filters.startDate || undefined}
                  max={new Date().toISOString().split("T")[0]} // Can't select future dates
                />
                {filters.endDate && (
                  <button
                    onClick={() => handleFilterChange("endDate", "")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
            {/* Entity Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <FileText className="h-3 w-3" />
                Entity
              </label>
              <Select
                value={filters.endpoint || "all"}
                onValueChange={(value) => handleFilterChange("endpoint", value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue placeholder="Select entity..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                      All Entities
                    </div>
                  </SelectItem>
                  <SelectItem value="Accounts">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      Accounts
                    </div>
                  </SelectItem>
                  <SelectItem value="Bank Transactions">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      Bank Transactions
                    </div>
                  </SelectItem>
                  <SelectItem value="Journals">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                      Journals
                    </div>
                  </SelectItem>
                  <SelectItem value="Invoices">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      Invoices
                    </div>
                  </SelectItem>
                  <SelectItem value="Payments">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                      Payments
                    </div>
                  </SelectItem>
                  <SelectItem value="Manual Journals">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-indigo-500"></div>
                      Manual Journals
                    </div>
                  </SelectItem>
                  <SelectItem value="Credit Notes">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      Credit Notes
                    </div>
                  </SelectItem>
                  <SelectItem value="Tracking Categories">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                      Tracking Categories
                    </div>
                  </SelectItem>
                  <SelectItem value="Tax Rates">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-pink-500"></div>
                      Tax Rates
                    </div>
                  </SelectItem>
                  <SelectItem value="Attachments">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-cyan-500"></div>
                      Attachments
                    </div>
                  </SelectItem>
                  <SelectItem value="Reports (P&L, Balance Sheet, Trial Balance)">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                      Reports (P&L, Balance Sheet, Trial Balance)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Status
              </label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue placeholder="Select status..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                      All Statuses
                    </div>
                  </SelectItem>
                  <SelectItem value="success">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      Success
                    </div>
                  </SelectItem>
                  <SelectItem value="warning">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-3 w-3 text-yellow-500" />
                      Warning
                    </div>
                  </SelectItem>
                  <SelectItem value="error">
                    <div className="flex items-center gap-2">
                      <XCircle className="h-3 w-3 text-red-500" />
                      Error
                    </div>
                  </SelectItem>
                  <SelectItem value="pending">
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-blue-500" />
                      Pending
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Display */}
          {(filters.searchQuery ||
            filters.startDate ||
            filters.endDate ||
            (filters.endpoint && filters.endpoint !== "all") ||
            (filters.status && filters.status !== "all")) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-sm font-medium text-gray-600">
                  Active filters:
                </span>

                {filters.searchQuery && (
                  <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs">
                    <Search className="h-3 w-3" />
                    Search: "{filters.searchQuery}"
                    <button
                      onClick={() => {
                        dispatch(setFilters({ searchQuery: "" }));
                        if (selectedOrganization) {
                          debouncedSearch("");
                        }
                      }}
                      className="ml-1 hover:text-blue-900"
                    >
                      <XCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}

                {filters.startDate && (
                  <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs">
                    <Calendar className="h-3 w-3" />
                    From: {filters.startDate}
                    <button
                      onClick={() => handleFilterChange("startDate", "")}
                      className="ml-1 hover:text-green-900"
                    >
                      <XCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}

                {filters.endDate && (
                  <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-md text-xs">
                    <Calendar className="h-3 w-3" />
                    To: {filters.endDate}
                    <button
                      onClick={() => handleFilterChange("endDate", "")}
                      className="ml-1 hover:text-green-900"
                    >
                      <XCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}

                {filters.endpoint && filters.endpoint !== "all" && (
                  <div className="flex items-center gap-1 bg-purple-100 text-purple-800 px-2 py-1 rounded-md text-xs">
                    <FileText className="h-3 w-3" />
                    Entity: {filters.endpoint}
                    <button
                      onClick={() => handleFilterChange("endpoint", "all")}
                      className="ml-1 hover:text-purple-900"
                    >
                      <XCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}

                {filters.status && filters.status !== "all" && (
                  <div className="flex items-center gap-1 bg-orange-100 text-orange-800 px-2 py-1 rounded-md text-xs">
                    <CheckCircle className="h-3 w-3" />
                    Status: {filters.status}
                    <button
                      onClick={() => handleFilterChange("status", "all")}
                      className="ml-1 hover:text-orange-900"
                    >
                      <XCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Sync History
              </CardTitle>
              <CardDescription>
                Showing {filteredLogs.length} log entries - Click any row to
                view details
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              {isLoading ? "Loading..." : "Refresh"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Entity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => {
                const isRetrying = retryingLogs.has(log.id);
                const canRetry =
                  log.status === "error" || log.status === "failed";

                return (
                  <TableRow
                    key={log.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleLogClick(log)}
                  >
                    <TableCell className="font-mono text-sm">
                      {log.timestamp}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        {log.endpoint}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(log.status)}
                        {getStatusBadge(log.status)}
                      </div>
                    </TableCell>
                    <TableCell>{log.duration}</TableCell>
                    <TableCell className="max-w-xs truncate">
                      {log.message}
                    </TableCell>
                    <TableCell>
                      {canRetry && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRetrySync(log.id);
                          }}
                          disabled={isRetrying || isLoading}
                          className={`
                            transition-all duration-200
                            ${
                              isRetrying
                                ? "bg-amber-100 border-amber-300 text-amber-700"
                                : "hover:bg-amber-50 hover:border-amber-300"
                            }
                          `}
                        >
                          {isRetrying ? (
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          ) : (
                            <RotateCcw className="h-3 w-3 mr-1" />
                          )}
                          {isRetrying ? "Retrying..." : "Retry"}
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>

        {/* Pagination */}
        {pagination && pagination.total > 0 && (
          <div className="px-6 py-4 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>
                  Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}{" "}
                  of {pagination.total} results
                </span>
                <Select
                  value={pagination.limit.toString()}
                  onValueChange={(value) => handleLimitChange(parseInt(value))}
                >
                  <SelectTrigger className="w-20 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span>per page</span>
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(pagination.page - 1)}
                      className={
                        pagination.hasPrev
                          ? "cursor-pointer"
                          : "cursor-not-allowed opacity-50"
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from(
                    {
                      length: Math.min(
                        5,
                        Math.ceil(pagination.total / pagination.limit)
                      ),
                    },
                    (_, i) => {
                      const pageNum = Math.max(1, pagination.page - 2) + i;
                      if (
                        pageNum > Math.ceil(pagination.total / pagination.limit)
                      )
                        return null;

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => handlePageChange(pageNum)}
                            isActive={pageNum === pagination.page}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    }
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(pagination.page + 1)}
                      className={
                        pagination.hasNext
                          ? "cursor-pointer"
                          : "cursor-not-allowed opacity-50"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        )}
      </Card>

      <Sheet open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <SheetContent side="right" className="w-[80vw] sm:w-[80vw] max-w-none">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {selectedLog?.endpoint}
            </SheetTitle>
            <SheetDescription>
              GET v1/{selectedLog?.endpoint.toLowerCase()}
            </SheetDescription>
          </SheetHeader>

          {selectedLog && (
            <div className="mt-6 space-y-6">
              {selectedLog &&
                (selectedLog.status === "error" ||
                  selectedLog.status === "failed") && (
                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleRetrySync(selectedLog.id)}
                      className={`
                      transition-all duration-200
                      ${
                        retryingLogs.has(selectedLog.id)
                          ? "bg-amber-600 hover:bg-amber-700"
                          : "bg-blue-500 hover:bg-blue-600"
                      }
                    `}
                      disabled={retryingLogs.has(selectedLog.id) || isLoading}
                    >
                      {retryingLogs.has(selectedLog.id) ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RotateCcw className="h-4 w-4 mr-2" />
                      )}
                      {retryingLogs.has(selectedLog.id)
                        ? "Retrying..."
                        : "Retry Sync"}
                    </Button>
                  </div>
                )}

              <div className="grid grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-sm mb-2">Integration</h4>
                  <p className="text-sm">Xero</p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">API</h4>
                  <p className="text-sm">
                    {selectedLog.endpoint.toLowerCase()}
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Timestamp</h4>
                  <p className="text-sm">{selectedLog.timestamp}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-sm mb-2">Status</h4>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">
                      {selectedLog.status === "success"
                        ? "200"
                        : selectedLog.status === "error"
                        ? "429"
                        : "206"}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Duration</h4>
                  <p className="text-sm">
                    {selectedLog.duration.replace("min", "")}ms
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm mb-2">Full URL</h4>
                  <p className="text-sm truncate">{selectedLog.fullUrl}</p>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-sm mb-2">Request ID</h4>
                <p className="text-sm font-mono">{selectedLog.requestId}</p>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">METHOD & ENDPOINT</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          `GET v1/${selectedLog.endpoint.toLowerCase()}`
                        )
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded">
                    <span className="text-blue-500 font-semibold">GET</span> v1/
                    {selectedLog.endpoint.toLowerCase()}
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">HEADERS</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard("content-type:\napplication/json")
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded">
                    <div className="text-sm">
                      <div>
                        <strong>content-type:</strong>
                      </div>
                      <div>application/json</div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm">RESPONSE BODY</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(selectedLog.responseBody || "")
                      }
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <div className="bg-muted p-3 rounded max-h-40 overflow-y-auto">
                    <pre className="text-sm whitespace-pre-wrap">
                      {JSON.stringify(
                        JSON.parse(selectedLog.responseBody || "{}"),
                        null,
                        2
                      )}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default SyncLogs;
