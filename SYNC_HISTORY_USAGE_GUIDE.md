# Sync History Usage Guide

This guide explains how to use the fully functional Sync History screen with pagination, filtering, search, and retry functionality.

## Overview

The Sync History screen provides comprehensive logging and management of all sync operations with the following features:

- **Pagination**: Navigate through large sets of sync logs efficiently
- **Search**: Find specific logs using text search
- **Filtering**: Filter by date range, entity, and status
- **Retry**: Retry failed sync operations
- **Real-time Updates**: Live status updates and toast notifications
- **Detailed View**: Expandable log details with full request/response data

## API Endpoints

The system uses two main API endpoints:

### 1. Get Sync Logs
```
GET /api/v1/companies/:companyId/sync-logs
```

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)
- `startDate` - Filter start date (YYYY-MM-DD)
- `endDate` - Filter end date (YYYY-MM-DD)
- `entity` - Filter by entity name
- `status` - Filter by status (success, error, warning)
- `search` - Search term for log messages
- `sortBy` - Sort field (timestamp, entity, status, duration)
- `sortOrder` - Sort direction (asc, desc)

**Response:**
```json
{
  "items": [
    {
      "id": "log_123",
      "timestamp": "2024-06-12T10:30:00Z",
      "entity": "Accounts",
      "status": "success",
      "duration": "2.3s",
      "message": "Successfully synced 45 accounts",
      "recordCount": 45,
      "endpoint": "accounts"
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 2. Retry Sync Operation
```
POST /api/v1/sync-logs/:syncLogId/retry
```

**Response:**
```json
{
  "success": true,
  "message": "Sync operation queued for retry",
  "newSyncLogId": "log_456"
}
```

## Component Features

### Pagination

The component includes a comprehensive pagination system:

```typescript
// Pagination controls
const handlePageChange = (page: number) => {
  dispatch(setPagination({ page }));
};

const handleLimitChange = (limit: number) => {
  dispatch(setPagination({ page: 1, limit }));
};
```

**Features:**
- Page navigation with Previous/Next buttons
- Direct page number selection
- Items per page selector (10, 20, 50, 100)
- Total results counter
- Smart page number display (shows 5 pages around current)

### Search Functionality

Real-time search with debouncing:

```typescript
// Debounced search (300ms delay)
const debouncedSearch = useCallback(
  debounce((searchQuery: string) => {
    dispatch(setFilters({ searchQuery }));
    if (selectedOrganization) {
      fetchLogsWithFilters({ ...filters, searchQuery, page: 1 });
    }
  }, 300),
  [dispatch, filters, selectedOrganization]
);
```

**Search Features:**
- Searches across log messages, entities, and error details
- 300ms debounce to prevent excessive API calls
- Automatically resets to page 1 when searching
- Visual search icon in input field

### Filtering System

Multiple filter options:

```typescript
const handleFilterChange = (filterType: string, value: string) => {
  dispatch(setFilters({ [filterType]: value }));
  dispatch(setPagination({ page: 1 })); // Reset to first page
  if (selectedOrganization) {
    fetchLogsWithFilters({ [filterType]: value, page: 1 });
  }
};
```

**Available Filters:**
- **Date Range**: Start and end date pickers
- **Entity**: Dropdown with all available sync entities
- **Status**: Success, Warning, Error, All
- **Search**: Free text search across log content

### Retry Functionality

Retry failed sync operations with full feedback:

```typescript
const handleRetrySync = async (syncLogId: string) => {
  try {
    setRetryingLogs(prev => new Set(prev).add(syncLogId));
    
    toast.loading("Retrying sync operation...", {
      id: `retry-${syncLogId}`,
    });

    await dispatch(retrySyncOperation(syncLogId)).unwrap();
    
    toast.success("Sync operation retried successfully!", {
      id: `retry-${syncLogId}`,
      description: "The sync operation has been queued for retry.",
    });

    handleRefresh(); // Refresh logs
  } catch (error) {
    toast.error("Failed to retry sync operation", {
      id: `retry-${syncLogId}`,
      description: typeof error === 'string' ? error : 'Please try again later.',
    });
  } finally {
    setRetryingLogs(prev => {
      const newSet = new Set(prev);
      newSet.delete(syncLogId);
      return newSet;
    });
  }
};
```

**Retry Features:**
- Only available for failed/error status logs
- Loading states with spinner animation
- Toast notifications for feedback
- Automatic log refresh after retry
- Prevents multiple simultaneous retries

## Usage Examples

### Basic Component Usage

```typescript
import SyncLogs from '@/components/SyncLogs';

function Dashboard() {
  return (
    <div>
      <SyncLogs />
    </div>
  );
}
```

### Redux Integration

```typescript
// Fetch logs with filters
const fetchLogsWithFilters = useCallback((customFilters?: any) => {
  if (!selectedOrganization) return;
  
  const finalFilters = {
    companyId: selectedOrganization,
    page: pagination?.page || 1,
    limit: pagination?.limit || 20,
    ...filters,
    ...customFilters,
  };
  
  dispatch(fetchSyncLogs(finalFilters));
}, [dispatch, selectedOrganization, filters, pagination]);

// Update pagination
dispatch(setPagination({ page: 2, limit: 50 }));

// Update filters
dispatch(setFilters({ 
  status: 'error', 
  startDate: '2024-06-01',
  endDate: '2024-06-12'
}));
```

### API Integration Example

```typescript
// Direct API usage
import { syncLogsApi } from '@/api/syncLogs.api';

const fetchSyncLogs = async () => {
  try {
    const result = await syncLogsApi.getCompanySyncLogs({
      companyId: 'company-uuid',
      page: 1,
      limit: 20,
      status: 'error',
      search: 'accounts'
    });
    
    console.log('Logs:', result.items);
    console.log('Pagination:', result.pagination);
  } catch (error) {
    console.error('Failed to fetch logs:', error);
  }
};

const retrySync = async (syncLogId: string) => {
  try {
    const result = await syncLogsApi.retrySyncOperation(syncLogId);
    console.log('Retry result:', result);
  } catch (error) {
    console.error('Retry failed:', error);
  }
};
```

## UI Components

### Table Features
- Sortable columns
- Status indicators with icons
- Clickable rows for detailed view
- Retry buttons for failed operations
- Loading states during operations

### Pagination UI
- Previous/Next navigation
- Page number buttons
- Items per page selector
- Results counter
- Responsive design

### Filter Panel
- Date range pickers
- Entity dropdown
- Status dropdown
- Search input with icon
- Clear filters option

### Detail Sheet
- Expandable side panel
- Full log details
- Request/response data
- Retry button for failed operations
- Copy functionality for debugging

## Best Practices

1. **Performance**: Use pagination to handle large datasets
2. **Search**: Implement debounced search to reduce API calls
3. **Error Handling**: Provide clear feedback for all operations
4. **Loading States**: Show loading indicators during async operations
5. **Accessibility**: Ensure keyboard navigation and screen reader support
6. **Responsive**: Design works on all screen sizes

## Troubleshooting

### Common Issues

1. **No logs showing**: Check if organization is selected
2. **Pagination not working**: Verify API returns pagination metadata
3. **Search not working**: Check debounce implementation
4. **Retry failing**: Verify sync log ID and API endpoint

### Debug Tips

```typescript
// Enable debug logging
console.log('Current filters:', filters);
console.log('Pagination state:', pagination);
console.log('Selected organization:', selectedOrganization);
```

The Sync History screen is now fully functional with all requested features including pagination, search, filtering, retry functionality, and comprehensive user feedback!
