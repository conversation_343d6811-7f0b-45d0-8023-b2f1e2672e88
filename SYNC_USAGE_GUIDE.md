# Sync Functionality Usage Guide

This guide explains how to use the sync functionality for syncing entities with proper validation and type safety.

## Overview

The sync system allows you to synchronize various Xero entities with validation using Zod schemas. It supports:

- Single entity sync
- Multiple entity sync
- Full sync of all entities
- Priority levels (HIGH, NORMAL, LOW)
- Full sync vs incremental sync options

## Request Structure

All sync requests must follow this structure:

```typescript
{
  companyId: string,        // UUID format required
  entities: string[],       // Array of valid entity names (no duplicates, min 1)
  priority?: 'HIGH' | 'NORMAL' | 'LOW',  // Optional, defaults to 'NORMAL'
  fullSync?: boolean        // Optional, defaults to false
}
```

## Available Entities

The following entities are supported for syncing:

- `Accounts`
- `Bank Transactions`
- `Bank Transfers`
- `Budgets`
- `Contacts`
- `Credit Notes`
- `Invoices`
- `Items`
- `Journals`
- `Manual Journals`
- `Payments`
- `Purchase Orders`
- `Quotes`
- `Receipts`
- `Repeating Invoices`
- `Tracking Categories`
- `Tax Rates`
- `Attachments`
- `Reports` (automatically maps to `BalanceSheet`, `ProfitLoss`, `TrialBalance`)
- `BalanceSheet` (individual report entity)
- `ProfitLoss` (individual report entity)
- `TrialBalance` (individual report entity)

## Special Entity Handling

### Reports Entity Mapping

When you specify `Reports` as an entity, it automatically maps to three individual report entities:

- `BalanceSheet`
- `ProfitLoss`
- `TrialBalance`

This means:

```typescript
// When you sync "Reports"
const reportsSync = createSingleEntitySyncRequest(companyId, "Reports");
// It actually syncs: ["BalanceSheet", "ProfitLoss", "TrialBalance"]

// For full sync, "Reports" is also expanded
const fullSync = createFullSyncRequest(companyId);
// "Reports" becomes ["BalanceSheet", "ProfitLoss", "TrialBalance"] in the final request
```

## Priority Levels

- `HIGH` - Urgent sync, processed first
- `NORMAL` - Standard priority (default)
- `LOW` - Lower priority, processed when resources available

## Usage Examples

### 1. Single Entity Sync

```typescript
import { createSingleEntitySyncRequest } from "@/utils/sync.utils";
import { triggerSync } from "@/store/actions/sync.actions";

// Basic single entity sync
const syncRequest = createSingleEntitySyncRequest(
  "123e4567-e89b-12d3-a456-************", // companyId
  "Accounts" // entity name
);

// Single entity sync with options
const prioritySync = createSingleEntitySyncRequest(
  "123e4567-e89b-12d3-a456-************",
  "Bank Transactions",
  { priority: "HIGH", fullSync: true }
);

// Dispatch the sync
dispatch(triggerSync(syncRequest));
```

### 2. Multiple Entity Sync

```typescript
import { createMultiEntitySyncRequest } from "@/utils/sync.utils";

// Sync multiple financial entities
const financialSync = createMultiEntitySyncRequest(
  "123e4567-e89b-12d3-a456-************",
  ["Accounts", "Bank Transactions", "Invoices", "Payments"],
  { priority: "HIGH", fullSync: false }
);

dispatch(triggerSync(financialSync));
```

### 3. Full Sync

```typescript
import { createFullSyncRequest } from "@/utils/sync.utils";

// Full sync of all entities
const fullSync = createFullSyncRequest("123e4567-e89b-12d3-a456-************", {
  priority: "HIGH",
});

// Full sync of specific entities only
const partialFullSync = createFullSyncRequest(
  "123e4567-e89b-12d3-a456-************",
  {
    priority: "NORMAL",
    entities: ["Accounts", "Contacts", "Invoices"],
  }
);

dispatch(triggerSync(fullSync));
```

### 4. Manual Request Construction

```typescript
// Manual construction following the schema
const manualRequest = {
  companyId: "123e4567-e89b-12d3-a456-************",
  entities: ["Accounts", "Contacts"],
  priority: "NORMAL" as const,
  fullSync: false,
};

dispatch(triggerSync(manualRequest));
```

### 5. React Component Integration

```typescript
import React from "react";
import { useAppDispatch } from "@/store/hooks";
import { triggerSync } from "@/store/actions/sync.actions";
import { createSingleEntitySyncRequest } from "@/utils/sync.utils";

const SyncComponent: React.FC = () => {
  const dispatch = useAppDispatch();
  const companyId = "your-company-uuid";

  const handleSyncAccounts = async () => {
    try {
      const request = createSingleEntitySyncRequest(companyId, "Accounts", {
        priority: "HIGH",
      });

      await dispatch(triggerSync(request)).unwrap();
      console.log("Sync completed successfully");
    } catch (error) {
      console.error("Sync failed:", error);
    }
  };

  return <button onClick={handleSyncAccounts}>Sync Accounts</button>;
};
```

## Validation

The system automatically validates all sync requests using Zod schemas. Common validation errors:

### Invalid Company ID

```typescript
// ❌ Invalid - not a UUID
{ companyId: "invalid-id", entities: ['Accounts'] }

// ✅ Valid - proper UUID format
{ companyId: "123e4567-e89b-12d3-a456-************", entities: ['Accounts'] }
```

### Invalid Entities

```typescript
// ❌ Invalid - empty array
{ companyId: "...", entities: [] }

// ❌ Invalid - unsupported entity
{ companyId: "...", entities: ['InvalidEntity'] }

// ❌ Invalid - duplicate entities
{ companyId: "...", entities: ['Accounts', 'Accounts'] }

// ✅ Valid
{ companyId: "...", entities: ['Accounts', 'Contacts'] }
```

### Invalid Priority

```typescript
// ❌ Invalid priority
{ companyId: "...", entities: ['Accounts'], priority: 'INVALID' }

// ✅ Valid priorities
{ companyId: "...", entities: ['Accounts'], priority: 'HIGH' }
{ companyId: "...", entities: ['Accounts'], priority: 'NORMAL' }
{ companyId: "...", entities: ['Accounts'], priority: 'LOW' }
```

## Entity Categories

For easier management, entities can be grouped by category:

```typescript
const categories = {
  financial: [
    "Accounts",
    "Bank Transactions",
    "Bank Transfers",
    "Invoices",
    "Payments",
    "Receipts",
  ],
  inventory: ["Items", "Purchase Orders", "Quotes"],
  contacts: ["Contacts"],
  reporting: ["Reports (P&L, Balance Sheet, Trial Balance)", "Budgets"],
  configuration: ["Tax Rates", "Tracking Categories"],
  documents: ["Attachments", "Credit Notes", "Repeating Invoices"],
  journals: ["Journals", "Manual Journals"],
};

// Sync all financial entities
const financialSync = createMultiEntitySyncRequest(
  companyId,
  categories.financial,
  { priority: "HIGH" }
);
```

## Error Handling

Always wrap sync operations in try-catch blocks:

```typescript
const handleSync = async () => {
  try {
    const request = createSingleEntitySyncRequest(companyId, "Accounts");
    const result = await dispatch(triggerSync(request)).unwrap();

    // Handle success
    console.log("Sync successful:", result);
  } catch (error) {
    // Handle validation or API errors
    console.error("Sync failed:", error);

    // Show user-friendly error message
    if (error.includes("Validation failed")) {
      alert("Invalid sync request. Please check your parameters.");
    } else {
      alert("Sync failed. Please try again later.");
    }
  }
};
```

## Best Practices

1. **Always validate company ID**: Ensure it's a valid UUID
2. **Use helper functions**: Prefer `createSingleEntitySyncRequest()` over manual construction
3. **Handle errors gracefully**: Always use try-catch with async operations
4. **Choose appropriate priority**: Use HIGH only for urgent syncs
5. **Consider full sync carefully**: Full sync takes longer and uses more resources
6. **Group related entities**: Sync related entities together for efficiency

## Utility Functions

The following utility functions are available in `@/utils/sync.utils`:

- `createSingleEntitySyncRequest()` - Create request for one entity
- `createMultiEntitySyncRequest()` - Create request for multiple entities
- `createFullSyncRequest()` - Create full sync request
- `isValidSyncEntity()` - Check if entity name is valid
- `getAllSyncEntities()` - Get all available entities
- `getAllSyncPriorities()` - Get all available priorities
- `getEntitiesByCategory()` - Get entities grouped by category

## Constants

Import these constants for type safety:

```typescript
import { SYNC_ENTITIES, SYNC_PRIORITIES } from "@/lib/validation";

// Use in your code
const isValidEntity = SYNC_ENTITIES.includes(entityName);
const defaultPriority = SYNC_PRIORITIES[1]; // 'NORMAL'
```
